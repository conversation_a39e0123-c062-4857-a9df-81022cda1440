<LoginScreen>:
    BoxLayout:
        orientation: 'vertical'
        padding: dp(30)
        spacing: dp(20)
        
        canvas.before:
            Color:
                rgba: 0.1, 0.1, 0.1, 1
            Rectangle:
                pos: self.pos
                size: self.size
        
        Widget:
            size_hint_y: 0.1
        
        Image:
            source: 'assets/logo.png'  # Logo image (create this file)
            size_hint: None, None
            size: dp(150), dp(150)
            pos_hint: {'center_x': 0.5}
        
        MDLabel:
            text: "محلات أبو علاء"
            halign: 'center'
            font_style: 'H4'
            theme_text_color: "Custom"
            text_color: app.theme_cls.primary_color
            size_hint_y: None
            height: self.texture_size[1]
            font_name: "NotoSansArabic"
        
        MDLabel:
            text: "نظام إدارة الديون والمبيعات"
            halign: 'center'
            theme_text_color: "Secondary"
            size_hint_y: None
            height: self.texture_size[1]
            font_name: "NotoSansArabic"
        
        Widget:
            size_hint_y: 0.1
        
        MDCard:
            orientation: 'vertical'
            padding: dp(20)
            spacing: dp(10)
            size_hint: None, None
            size: dp(400), dp(250)
            pos_hint: {'center_x': 0.5}
            elevation: 4
            md_bg_color: 0.15, 0.15, 0.15, 1
            
            MDLabel:
                text: "تسجيل الدخول"
                halign: 'center'
                font_style: 'H5'
                size_hint_y: None
                height: self.texture_size[1]
                font_name: "NotoSansArabic"
            
            MDTextField:
                id: username
                hint_text: "اسم المستخدم"
                icon_right: "account"
                size_hint_x: None
                width: dp(300)
                pos_hint: {'center_x': 0.5}
                font_name: "NotoSansArabic"
                hint_text_color_normal: app.theme_cls.primary_color
            
            MDTextField:
                id: password
                hint_text: "كلمة المرور"
                icon_right: "lock"
                size_hint_x: None
                width: dp(300)
                pos_hint: {'center_x': 0.5}
                password: True
                font_name: "NotoSansArabic"
                hint_text_color_normal: app.theme_cls.primary_color
            
            Widget:
                size_hint_y: None
                height: dp(10)
            
            MDRaisedButton:
                text: "دخول"
                pos_hint: {'center_x': 0.5}
                size_hint_x: 0.7
                on_release: root.verify_login()
                md_bg_color: app.theme_cls.primary_color
                font_name: "NotoSansArabic"
        
        Widget:
            size_hint_y: 0.2
        
        MDLabel:
            text: "برمجة: Khalid Shujaa"
            halign: 'center'
            theme_text_color: "Hint"
            font_style: 'Caption'
            size_hint_y: None
            height: self.texture_size[1]
            font_name: "NotoSansArabic"