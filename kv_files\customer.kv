<CustomerScreen>:
    BoxLayout:
        orientation: 'vertical'
        
        MDTopAppBar:
            title: "تفاصيل الزبون"
            elevation: 4
            left_action_items: [['arrow-left', lambda x: root.go_back()]]
            right_action_items: [['pencil', lambda x: root.edit_customer()]]
            font_name: "NotoSansArabic"
        
        MDBoxLayout:
            orientation: 'vertical'
            padding: dp(16)
            spacing: dp(16)
            
            # Customer Info Card
            MDCard:
                orientation: 'vertical'
                padding: dp(16)
                md_bg_color: 0.15, 0.15, 0.15, 1
                size_hint_y: None
                height: dp(180)
                
                MDBoxLayout:
                    orientation: 'horizontal'
                    
                    # Customer Basic Info
                    MDBoxLayout:
                        orientation: 'vertical'
                        spacing: dp(8)
                        size_hint_x: 0.6
                        
                        MDLabel:
                            id: customer_name
                            text: "اسم الزبون"
                            font_style: 'H5'
                            theme_text_color: "Primary"
                            font_name: "NotoSansArabic"
                        
                        MDBoxLayout:
                            adaptive_height: True
                            spacing: dp(4)
                            
                            MDIcon:
                                icon: "phone"
                                theme_text_color: "Secondary"
                            
                            MDLabel:
                                id: customer_phone
                                text: "رقم الهاتف"
                                theme_text_color: "Secondary"
                                font_name: "NotoSansArabic"
                        
                        MDBoxLayout:
                            adaptive_height: True
                            spacing: dp(4)
                            
                            MDIcon:
                                icon: "book-open-page-variant"
                                theme_text_color: "Secondary"
                            
                            MDLabel:
                                id: customer_page
                                text: "رقم الصفحة"
                                theme_text_color: "Secondary"
                        
                        MDBoxLayout:
                            adaptive_height: True
                            spacing: dp(4)
                            
                            MDIcon:
                                icon: "calendar"
                                theme_text_color: "Secondary"
                            
                            MDLabel:
                                id: customer_since
                                text: "زبون منذ"
                                theme_text_color: "Secondary"
                    
                    # Customer Financial Summary
                    MDBoxLayout:
                        orientation: 'vertical'
                        spacing: dp(8)
                        size_hint_x: 0.4
                        
                        MDLabel:
                            text: "ملخص مالي"
                            font_style: 'H6'
                            theme_text_color: "Primary"
                        
                        MDBoxLayout:
                            adaptive_height: True
                            spacing: dp(4)
                            
                            MDLabel:
                                text: "إجمالي الديون:"
                                theme_text_color: "Secondary"
                                size_hint_x: 0.6
                            
                            MDLabel:
                                id: total_debt
                                text: "0.00"
                                theme_text_color: "Custom"
                                text_color: 1, 0.5, 0.5, 1  # Light red
                        
                        MDBoxLayout:
                            adaptive_height: True
                            spacing: dp(4)
                            
                            MDLabel:
                                text: "إجمالي المدفوعات:"
                                theme_text_color: "Secondary"
                                size_hint_x: 0.6
                            
                            MDLabel:
                                id: total_paid
                                text: "0.00"
                                theme_text_color: "Custom"
                                text_color: 0.5, 1, 0.5, 1  # Light green
                        
                        MDBoxLayout:
                            adaptive_height: True
                            spacing: dp(4)
                            
                            MDLabel:
                                text: "الرصيد المتبقي:"
                                theme_text_color: "Secondary"
                                size_hint_x: 0.6
                            
                            MDLabel:
                                id: balance
                                text: "0.00"
                                theme_text_color: "Custom"
                                text_color: 0.5, 0.7, 1, 1  # Light blue
            
            # Action Buttons
            MDBoxLayout:
                adaptive_height: True
                spacing: dp(10)
                
                MDRaisedButton:
                    text: "إضافة معاملة"
                    on_release: root.add_transaction()
                    md_bg_color: app.theme_cls.primary_color
            
            # Main Content Area
            MDBoxLayout:
                orientation: 'vertical'
                spacing: dp(16)
                
                # Transactions Table
                MDCard:
                    orientation: 'vertical'
                    padding: dp(16)
                    md_bg_color: 0.15, 0.15, 0.15, 1
                    
                    MDLabel:
                        text: "سجل المعاملات"
                        theme_text_color: "Primary"
                        font_style: 'H6'
                        size_hint_y: None
                        height: dp(30)
                    
                    MDBoxLayout:
                        id: table_container
                        # Will be populated with MDDataTable
                
                # Chart
                MDCard:
                    orientation: 'vertical'
                    padding: dp(16)
                    md_bg_color: 0.15, 0.15, 0.15, 1
                    size_hint_y: 0.4
                    
                    MDLabel:
                        text: "الرسم البياني"
                        theme_text_color: "Primary"
                        font_style: 'H6'
                        size_hint_y: None
                        height: dp(30)
                    
                    Image:
                        id: customer_chart
                        source: 'customer_chart.png'  # Will be generated dynamically
                        allow_stretch: True
                        keep_ratio: True
        
        # Footer
        MDBoxLayout:
            adaptive_height: True
            padding: [0, dp(8), 0, dp(8)]
            md_bg_color: app.theme_cls.primary_color
            
            MDLabel:
                text: "برمجة: Khalid Shujaa"
                halign: 'center'
                theme_text_color: "Custom"
                text_color: 1, 1, 1, 1
                font_style: 'Caption'

# New Transaction Form
<NewTransactionForm@MDBoxLayout>:
    orientation: 'vertical'
    spacing: dp(10)
    size_hint_y: None
    height: dp(200)
    padding: dp(20)
    
    MDTextField:
        id: amount
        hint_text: "المبلغ"
        helper_text: "مطلوب"
        helper_text_mode: "on_error"
        input_filter: "float"
    
    MDTextField:
        id: description
        hint_text: "الوصف"
        multiline: True
    
    MDBoxLayout:
        adaptive_height: True
        spacing: dp(10)
        
        MDLabel:
            text: "نوع المعاملة:"
            theme_text_color: "Secondary"
            size_hint_x: 0.3
        
        MDSegmentedControl:
            id: transaction_type
            md_bg_color: app.theme_cls.primary_light
            segment_color: app.theme_cls.primary_color
            
            MDSegmentedControlItem:
                text: "دين"
            
            MDSegmentedControlItem:
                text: "دفعة"

# Edit Customer Form
<EditCustomerForm@MDBoxLayout>:
    orientation: 'vertical'
    spacing: dp(10)
    size_hint_y: None
    height: dp(200)
    padding: dp(20)
    
    MDTextField:
        id: name
        hint_text: "اسم الزبون"
        helper_text: "مطلوب"
        helper_text_mode: "on_error"
    
    MDTextField:
        id: phone
        hint_text: "رقم الهاتف"
        input_filter: "int"
    
    MDTextField:
        id: page_number
        hint_text: "رقم الصفحة"