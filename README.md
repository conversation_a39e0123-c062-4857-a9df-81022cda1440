# محلات أبو علاء - نظام إدارة الديون والمبيعات

تطبيق سطح المكتب وأندرويد لإدارة الديون والمبيعات لمحلات أبو علاء. يوفر التطبيق واجهة مستخدم عصرية وجذابة مع ميزات متقدمة لإدارة الزبائن والديون والمدفوعات.

## الميزات الرئيسية

- **واجهة مستخدم عصرية**: تصميم داكن (Dark Mode) مع حركات أنيميشن ناعمة وخفيفة.
- **شاشة دخول آمنة**: تسجيل دخول بسيط وآمن للنظام.
- **لوحة تحكم شاملة**: عرض ملخص للديون والمدفوعات والزبائن مع رسوم بيانية تفاعلية.
- **إدارة الزبائن**: عرض بيانات كل زبون مع سجل معاملاته ورسم بياني لتاريخه المالي.
- **استيراد البيانات**: إمكانية استيراد بيانات الزبائن والديون من ملفات Excel.
- **نظام تنبيهات**: تنبيهات ذكية للديون المتأخرة.

## المتطلبات

- Python 3.7 أو أحدث
- المكتبات المطلوبة (مذكورة في ملف requirements.txt)

## التثبيت

1. قم بتثبيت Python من [الموقع الرسمي](https://www.python.org/downloads/)
2. قم بتنزيل أو استنساخ هذا المستودع
3. قم بإنشاء بيئة افتراضية:
   ```
   python -m venv venv
   ```
4. قم بتنشيط البيئة الافتراضية:
   - في Windows:
     ```
     venv\Scripts\activate
     ```
   - في Linux/Mac:
     ```
     source venv/bin/activate
     ```
5. قم بتثبيت المكتبات المطلوبة:
   ```
   pip install -r requirements.txt
   ```

## التشغيل

```
python main.py
```

## بنية المشروع

- `main.py`: نقطة البداية للتطبيق
- `kv_files/`: ملفات تصميم واجهة المستخدم
- `assets/`: الصور والخطوط المستخدمة في التطبيق
- `abu_alaa_store.db`: قاعدة البيانات SQLite

## المعمارية

يستخدم التطبيق نمط Model-View-Controller (MVC) لفصل منطق العمل عن واجهة المستخدم:

- **Model**: قاعدة بيانات SQLite لتخزين بيانات الزبائن والمعاملات والتنبيهات
- **View**: ملفات KV لتعريف واجهة المستخدم باستخدام KivyMD
- **Controller**: الفئات البرمجية في Python التي تتحكم في تدفق البيانات وتفاعل المستخدم

## المكتبات المستخدمة

- **Kivy/KivyMD**: إطار عمل لبناء واجهات المستخدم متعددة المنصات
- **SQLite**: قاعدة بيانات خفيفة الوزن
- **Pandas**: لمعالجة البيانات واستيراد ملفات Excel
- **Matplotlib**: لإنشاء الرسوم البيانية
- **Plyer**: للوصول إلى ميزات النظام مثل الإشعارات

## المطور

برمجة: Khalid Shujaa