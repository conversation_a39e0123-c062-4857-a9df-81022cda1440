# البنية المعمارية لتطبيق محلات أبو علاء

## نظرة عامة

تم تصميم تطبيق "محلات أبو علاء" باستخدام نمط Model-View-Controller (MVC) لفصل منطق العمل عن واجهة المستخدم، مما يسهل الصيانة والتطوير المستقبلي. يستخدم التطبيق إطار عمل Kivy/KivyMD لبناء واجهة المستخدم، وSQLite كقاعدة بيانات، ومكتبات أخرى للرسوم البيانية ومعالجة البيانات.

## المكونات الرئيسية

### 1. نموذج البيانات (Model)

#### قاعدة البيانات SQLite

تتكون قاعدة البيانات من ثلاثة جداول رئيسية:

- **جدول الزبائن (customers)**: يخزن معلومات الزبائن الأساسية مثل الاسم ورقم الهاتف ورقم الصفحة.
- **جدول المعاملات (transactions)**: يخزن جميع المعاملات المالية، سواء كانت ديون أو مدفوعات، مع ربطها بالزبون المعني.
- **جدول التنبيهات (notifications)**: يخزن التنبيهات المتعلقة بالديون المتأخرة وحالتها.

### 2. واجهة المستخدم (View)

تم تنظيم واجهة المستخدم في ثلاث شاشات رئيسية:

- **شاشة تسجيل الدخول (LoginScreen)**: توفر واجهة آمنة للدخول إلى النظام.
- **لوحة التحكم (DashboardScreen)**: تعرض ملخصًا للبيانات المالية والزبائن والمعاملات الأخيرة مع رسوم بيانية.
- **شاشة الزبون (CustomerScreen)**: تعرض تفاصيل زبون محدد مع سجل معاملاته ورسم بياني لتاريخه المالي.

تم تعريف هذه الشاشات باستخدام ملفات KV المنفصلة لفصل منطق العمل عن التصميم.

### 3. وحدة التحكم (Controller)

تتكون وحدة التحكم من الفئات البرمجية التالية:

- **AbuAlaaStoreApp**: الفئة الرئيسية للتطبيق التي تدير الشاشات وتهيئ قاعدة البيانات.
- **LoginScreen**: تتحكم في عملية تسجيل الدخول والتحقق من صحة بيانات المستخدم.
- **DashboardScreen**: تدير عرض البيانات الملخصة والرسوم البيانية وعمليات البحث وإضافة الزبائن واستيراد البيانات.
- **CustomerScreen**: تتحكم في عرض بيانات الزبون وسجل معاملاته وإضافة معاملات جديدة.

## تدفق البيانات

1. **تهيئة التطبيق**: عند بدء التطبيق، يتم إنشاء قاعدة البيانات إذا لم تكن موجودة، وتحميل ملفات KV، وإنشاء الشاشات.
2. **تسجيل الدخول**: يدخل المستخدم بيانات الدخول، ويتم التحقق منها قبل الانتقال إلى لوحة التحكم.
3. **لوحة التحكم**: يتم استرجاع البيانات من قاعدة البيانات وعرضها في الواجهة، مع إمكانية البحث عن زبون أو إضافة زبون جديد.
4. **شاشة الزبون**: عند اختيار زبون، يتم استرجاع بياناته ومعاملاته وعرضها في الواجهة، مع إمكانية إضافة معاملات جديدة أو تعديل بياناته.

## المكتبات المستخدمة وأسباب اختيارها

### 1. Kivy/KivyMD

**السبب**: تم اختيار Kivy لأنه إطار عمل متعدد المنصات يدعم كلاً من سطح المكتب والأجهزة المحمولة، مما يتيح تطوير تطبيق واحد يعمل على كل من Windows وAndroid. KivyMD يوفر مكونات واجهة مستخدم بتصميم Material Design العصري.

### 2. SQLite

**السبب**: تم اختيار SQLite كقاعدة بيانات لأنها خفيفة الوزن ولا تحتاج إلى خادم منفصل، مما يجعلها مثالية للتطبيقات المستقلة. كما أنها توفر جميع ميزات قواعد البيانات العلائقية الأساسية.

### 3. Pandas

**السبب**: تم اختيار Pandas لمعالجة البيانات واستيراد ملفات Excel لأنها توفر أدوات قوية للتعامل مع البيانات المنظمة وتحليلها، مما يسهل عملية استيراد البيانات من مصادر خارجية.

### 4. Matplotlib

**السبب**: تم اختيار Matplotlib لإنشاء الرسوم البيانية لأنها مكتبة شاملة وقوية للرسوم البيانية في Python، وتوفر مرونة كبيرة في تخصيص الرسوم البيانية.

### 5. Plyer

**السبب**: تم اختيار Plyer للوصول إلى ميزات النظام مثل الإشعارات لأنها توفر واجهة موحدة للوصول إلى ميزات الأجهزة عبر منصات مختلفة، مما يسهل تنفيذ ميزات مثل الإشعارات بطريقة متوافقة مع كل من سطح المكتب والأجهزة المحمولة.

## ميزات الأمان

- **تسجيل الدخول**: يتم التحقق من بيانات المستخدم قبل السماح بالوصول إلى النظام.
- **حماية البيانات**: يتم تخزين البيانات في قاعدة بيانات SQLite محلية، مما يحد من مخاطر الوصول غير المصرح به.
- **التحقق من المدخلات**: يتم التحقق من صحة المدخلات قبل إضافتها إلى قاعدة البيانات لمنع هجمات حقن SQL.

## قابلية التوسع

تم تصميم التطبيق بطريقة تسمح بإضافة ميزات جديدة بسهولة:

- **إضافة شاشات جديدة**: يمكن إضافة شاشات جديدة عن طريق إنشاء فئة جديدة ترث من Screen وملف KV مقابل.
- **توسيع قاعدة البيانات**: يمكن إضافة جداول أو حقول جديدة لدعم ميزات إضافية.
- **إضافة تقارير**: يمكن إضافة تقارير جديدة باستخدام Pandas وMatplotlib لتحليل البيانات وعرضها.

## الخلاصة

تم تصميم تطبيق "محلات أبو علاء" باستخدام أفضل الممارسات في هندسة البرمجيات لإنشاء تطبيق قوي وقابل للصيانة والتوسع. يوفر التطبيق واجهة مستخدم عصرية وسهلة الاستخدام مع ميزات متقدمة لإدارة الديون والمبيعات، مما يساعد في تحسين كفاءة العمل وتتبع المعاملات المالية بدقة.