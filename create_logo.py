from PIL import Image, ImageDraw, ImageFont
import os
import arabic_reshaper
from bidi.algorithm import get_display

# Create a 512x512 image with a dark background
img = Image.new('RGBA', (512, 512), (33, 33, 33, 255))
draw = ImageDraw.Draw(img)

# Draw a circle for the background
circle_center = (256, 256)
circle_radius = 200
draw.ellipse(
    [(circle_center[0] - circle_radius, circle_center[1] - circle_radius),
     (circle_center[0] + circle_radius, circle_center[1] + circle_radius)],
    fill=(103, 58, 183, 255)  # Deep Purple
)

# Try to load a font for Arabic text
font_path = os.path.join('assets', 'fonts', 'NotoSansArabic-Bold.ttf')
if os.path.exists(font_path):
    try:
        font = ImageFont.truetype(font_path, 60)
    except IOError:
        font = ImageFont.load_default()
else:
    font = ImageFont.load_default()

# Draw the store icon (simplified representation)
store_color = (255, 255, 255, 255)  # White
roof_points = [(156, 200), (356, 200), (306, 150), (206, 150)]
draw.polygon(roof_points, fill=store_color)

# Draw store building
draw.rectangle([(186, 200), (326, 320)], fill=store_color)

# Draw door
draw.rectangle([(236, 250), (276, 320)], fill=(33, 33, 33, 255))

# Draw text with proper Arabic text reshaping and bidirectional support
text = "محلات أبو علاء"
# Reshape Arabic text for proper display
reshaped_text = arabic_reshaper.reshape(text)
bidi_text = get_display(reshaped_text)

# Calculate text position
text_bbox = draw.textbbox((0, 0), bidi_text, font=font)
text_width = text_bbox[2] - text_bbox[0]
text_position = ((512 - text_width) // 2, 350)

# Draw the text
draw.text(text_position, bidi_text, font=font, fill=(255, 255, 255, 255))

# Save the image
img.save(os.path.join('assets', 'logo.png'))

print("Logo created successfully!")