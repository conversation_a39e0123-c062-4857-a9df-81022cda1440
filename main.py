import os
import sqlite3
from datetime import datetime

# إضافة مكتبات دعم اللغة العربية
import arabic_reshaper
from bidi.algorithm import get_display

from kivy.lang import Builder
from kivy.metrics import dp
from kivy.properties import StringProperty, NumericProperty, ObjectProperty
from kivy.uix.screenmanager import ScreenManager, Screen
from kivy.core.window import Window
from kivy.clock import Clock
from kivy.utils import get_color_from_hex
from kivy.core.text import LabelBase

from kivymd.app import MDApp
from kivymd.uix.datatables import MDDataTable
from kivymd.uix.dialog import MDDialog
from kivymd.uix.button import MDFlatButton
from kivymd.uix.list import OneLineIconListItem, IconLeftWidget
from kivymd.uix.menu import MDDropdownMenu
from kivymd.uix.snackbar import Snackbar

def show_snackbar(text):
    """Helper function to show snackbar with proper Arabic text"""
    snackbar = Snackbar()
    snackbar.text = text
    snackbar.open()

import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.backends.backend_agg import FigureCanvasAgg as FigureCanvas
from plyer import notification

# Configure matplotlib for Arabic text support
plt.rcParams['font.family'] = ['DejaVu Sans', 'Arial Unicode MS', 'Tahoma', 'NotoSansArabic']
plt.rcParams['axes.unicode_minus'] = False

# Set window size for desktop
Window.size = (1200, 700)
Window.minimum_width, Window.minimum_height = 1000, 600

# Database initialization
def init_database():
    conn = sqlite3.connect('abu_alaa_store.db')
    cursor = conn.cursor()
    
    # Create customers table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS customers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        phone TEXT,
        page_number TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')
    
    # Create transactions table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS transactions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        customer_id INTEGER,
        amount REAL NOT NULL,
        is_debt INTEGER NOT NULL,  /* True for debt, False for payment */
        description TEXT,
        transaction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (customer_id) REFERENCES customers (id)
    )
    ''')
    
    # Create notifications table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS notifications (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        customer_id INTEGER,
        message TEXT NOT NULL,
        is_read BOOLEAN DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (customer_id) REFERENCES customers (id)
    )
    ''')
    
    conn.commit()
    conn.close()

# Main App Class
class AbuAlaaStoreApp(MDApp):
    # دالة لإعادة تشكيل النص العربي
    def reshape_arabic(self, text):
        if text:
            reshaped_text = arabic_reshaper.reshape(text)
            bidi_text = get_display(reshaped_text)
            return bidi_text
        return text
    
    def build(self):
        # Initialize database
        init_database()

        # تسجيل الخط العربي
        LabelBase.register(
            name="NotoSansArabic",
            fn_regular="assets/fonts/NotoSansArabic-Regular.ttf",
            fn_bold="assets/fonts/NotoSansArabic-Bold.ttf"
        )

        # Set theme
        self.theme_cls.theme_style = "Dark"
        self.theme_cls.primary_palette = "DeepPurple"
        self.theme_cls.accent_palette = "Teal"

        # Load KV files
        for kv_file in os.listdir('kv_files'):
            if kv_file.endswith('.kv'):
                Builder.load_file(os.path.join('kv_files', kv_file))

        # Create screen manager
        self.sm = ScreenManager()
        self.sm.add_widget(LoginScreen(name='login'))
        self.sm.add_widget(DashboardScreen(name='dashboard'))
        self.sm.add_widget(CustomerScreen(name='customer'))

        # Schedule notification check
        Clock.schedule_interval(self.check_overdue_debts, 3600)  # Check every hour

        return self.sm

    def on_start(self):
        # Fix Arabic text in UI after the app starts
        Clock.schedule_once(self.fix_arabic_text_in_ui, 0.5)

    def fix_arabic_text_in_ui(self, dt=None):
        # Fix Arabic text in all screens after they are loaded
        try:
            # Fix login screen
            login_screen = self.sm.get_screen('login')
            self.fix_login_screen_text(login_screen)

            # Fix dashboard screen
            dashboard_screen = self.sm.get_screen('dashboard')
            self.fix_dashboard_screen_text(dashboard_screen)
        except Exception as e:
            print(f"Error fixing Arabic text: {e}")

    def fix_login_screen_text(self, screen):
        try:
            # Find and fix all labels with Arabic text
            for widget in screen.walk():
                if hasattr(widget, 'text') and widget.text:
                    if any(ord(char) >= 0x0600 and ord(char) <= 0x06FF for char in widget.text):
                        widget.text = self.reshape_arabic(widget.text)
        except:
            pass

    def fix_dashboard_screen_text(self, screen):
        try:
            # Find and fix all labels with Arabic text
            for widget in screen.walk():
                if hasattr(widget, 'text') and widget.text:
                    if any(ord(char) >= 0x0600 and ord(char) <= 0x06FF for char in widget.text):
                        widget.text = self.reshape_arabic(widget.text)
        except:
            pass
    
    def check_overdue_debts(self, dt):
        # Connect to database
        conn = sqlite3.connect('abu_alaa_store.db')
        cursor = conn.cursor()
        
        # Get customers with overdue debts (more than 30 days)
        cursor.execute('''
        SELECT c.id, c.name, t.amount, t.transaction_date 
        FROM customers c
        JOIN transactions t ON c.id = t.customer_id
        WHERE t.is_debt = 1 AND 
              julianday('now') - julianday(t.transaction_date) > 30 AND
              t.id NOT IN (
                  SELECT customer_id FROM notifications
                  WHERE created_at > datetime('now', '-7 day')
              )
        ''')
        
        overdue_customers = cursor.fetchall()
        
        for customer in overdue_customers:
            customer_id, name, amount, date = customer
            message = self.reshape_arabic(f"تذكير: {name} لديه دين متأخر بقيمة {amount} منذ {date}")

            # Add notification to database
            cursor.execute('''
            INSERT INTO notifications (customer_id, message)
            VALUES (?, ?)
            ''', (customer_id, message))

            # Show system notification
            notification.notify(
                title=self.reshape_arabic('محلات أبو علاء - تذكير بدين متأخر'),
                message=message,
                timeout=10
            )
        
        conn.commit()
        conn.close()

# Login Screen
class LoginScreen(Screen):
    def on_enter(self):
        # Fix Arabic text when entering the screen
        self.fix_arabic_text()

    def fix_arabic_text(self):
        app = MDApp.get_running_app()
        try:
            # Fix title and subtitle
            if hasattr(self.ids, 'app_title'):
                self.ids.app_title.text = app.reshape_arabic("محلات أبو علاء")
            if hasattr(self.ids, 'app_subtitle'):
                self.ids.app_subtitle.text = app.reshape_arabic("نظام إدارة الديون والمبيعات")

            # Fix all Arabic text in the screen
            for widget in self.walk():
                if hasattr(widget, 'text') and widget.text:
                    if any(ord(char) >= 0x0600 and ord(char) <= 0x06FF for char in widget.text):
                        widget.text = app.reshape_arabic(widget.text)
                if hasattr(widget, 'hint_text') and widget.hint_text:
                    if any(ord(char) >= 0x0600 and ord(char) <= 0x06FF for char in widget.hint_text):
                        widget.hint_text = app.reshape_arabic(widget.hint_text)
        except Exception as e:
            print(f"Error fixing Arabic text in login: {e}")

    def verify_login(self):
        username = self.ids.username.text
        password = self.ids.password.text

        # Simple authentication (in a real app, use proper authentication)
        if username == "admin" and password == "admin123":
            self.manager.current = 'dashboard'
        else:
            app = MDApp.get_running_app()
            show_snackbar(app.reshape_arabic("اسم المستخدم أو كلمة المرور غير صحيحة"))

# Dashboard Screen
class DashboardScreen(Screen):
    def on_enter(self):
        # Fix Arabic text first
        self.fix_arabic_text()
        # Update dashboard data when entering the screen
        self.update_summary()
        self.update_recent_transactions()
        self.update_chart()

    def fix_arabic_text(self):
        app = MDApp.get_running_app()
        try:
            # Fix all Arabic text in the dashboard
            for widget in self.walk():
                if hasattr(widget, 'text') and widget.text:
                    if any(ord(char) >= 0x0600 and ord(char) <= 0x06FF for char in widget.text):
                        widget.text = app.reshape_arabic(widget.text)
                if hasattr(widget, 'hint_text') and widget.hint_text:
                    if any(ord(char) >= 0x0600 and ord(char) <= 0x06FF for char in widget.hint_text):
                        widget.hint_text = app.reshape_arabic(widget.hint_text)
                if hasattr(widget, 'title') and widget.title:
                    if any(ord(char) >= 0x0600 and ord(char) <= 0x06FF for char in widget.title):
                        widget.title = app.reshape_arabic(widget.title)
        except Exception as e:
            print(f"Error fixing Arabic text in dashboard: {e}")
    
    def update_summary(self):
        conn = sqlite3.connect('abu_alaa_store.db')
        cursor = conn.cursor()
        
        # Get total debts
        cursor.execute('''
        SELECT SUM(amount) FROM transactions WHERE is_debt = 1
        ''')
        total_debts = cursor.fetchone()[0] or 0
        
        # Get total payments
        cursor.execute('''
        SELECT SUM(amount) FROM transactions WHERE is_debt = 0
        ''')
        total_payments = cursor.fetchone()[0] or 0
        
        # Get overdue debts (more than 30 days)
        cursor.execute('''
        SELECT SUM(amount) FROM transactions 
        WHERE is_debt = 1 AND julianday('now') - julianday(transaction_date) > 30
        ''')
        overdue_debts = cursor.fetchone()[0] or 0
        
        # Get total customers
        cursor.execute('SELECT COUNT(*) FROM customers')
        total_customers = cursor.fetchone()[0] or 0
        
        conn.close()
        
        # Update UI
        self.ids.total_debts.text = f"{total_debts:.2f}"
        self.ids.total_payments.text = f"{total_payments:.2f}"
        self.ids.overdue_debts.text = f"{overdue_debts:.2f}"
        self.ids.total_customers.text = str(total_customers)
    
    def update_recent_transactions(self):
        conn = sqlite3.connect('abu_alaa_store.db')
        cursor = conn.cursor()
        
        # Get recent transactions
        cursor.execute('''
        SELECT c.name, t.amount, t.is_debt, t.transaction_date, t.description 
        FROM transactions t
        JOIN customers c ON t.customer_id = c.id
        ORDER BY t.transaction_date DESC LIMIT 10
        ''')
        
        transactions = cursor.fetchall()
        conn.close()
        
        # Clear previous transactions
        self.ids.recent_transactions_list.clear_widgets()
        
        # Add transactions to the list
        for transaction in transactions:
            name, amount, is_debt, date, description = transaction
            icon = "cash-plus" if is_debt else "cash-minus"
            color = "red" if is_debt else "green"
            
            item = OneLineIconListItem(
                text=f"{name} - {amount:.2f} - {date} - {description}"
            )
            item.add_widget(IconLeftWidget(icon=icon, theme_text_color="Custom", text_color=get_color_from_hex(color)))
            self.ids.recent_transactions_list.add_widget(item)
    
    def update_chart(self):
        conn = sqlite3.connect('abu_alaa_store.db')
        
        # Get monthly data for the last 6 months
        query = '''
        SELECT 
            strftime('%m-%Y', transaction_date) as month,
            SUM(CASE WHEN is_debt = 1 THEN amount ELSE 0 END) as debts,
            SUM(CASE WHEN is_debt = 0 THEN amount ELSE 0 END) as payments
        FROM transactions
        WHERE transaction_date >= date('now', '-6 months')
        GROUP BY month
        ORDER BY transaction_date
        '''
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        if df.empty:
            return
        
        # Create chart
        fig, ax = plt.subplots(figsize=(10, 4))

        months = df['month'].tolist()
        debts = df['debts'].tolist()
        payments = df['payments'].tolist()

        ax.bar(months, debts, color='red', alpha=0.7, label=self.reshape_arabic('ديون'))
        ax.bar(months, payments, color='green', alpha=0.7, label=self.reshape_arabic('مدفوعات'))

        ax.set_title(self.reshape_arabic('ملخص الديون والمدفوعات الشهرية'))
        ax.set_xlabel(self.reshape_arabic('الشهر'))
        ax.set_ylabel(self.reshape_arabic('المبلغ'))
        ax.legend()
        
        # Save chart to file
        plt.tight_layout()
        plt.savefig('chart.png')
        plt.close()
        
        # Update chart in UI
        self.ids.chart_image.reload()
    
    def search_customers(self, query):
        if not query:
            return
        
        conn = sqlite3.connect('abu_alaa_store.db')
        cursor = conn.cursor()
        
        # Search for customers
        cursor.execute('''
        SELECT id, name, phone FROM customers
        WHERE name LIKE ? OR phone LIKE ?
        LIMIT 10
        ''', (f'%{query}%', f'%{query}%'))
        
        customers = cursor.fetchall()
        conn.close()
        
        # Create dropdown menu items
        menu_items = [
            {
                "text": f"{name} - {phone}",
                "viewclass": "OneLineListItem",
                "on_release": lambda x=customer_id: self.open_customer(x),
            } for customer_id, name, phone in customers
        ]
        
        # Show dropdown menu
        self.dropdown_menu = MDDropdownMenu(
            caller=self.ids.search_field,
            items=menu_items,
            width_mult=4,
        )
        self.dropdown_menu.open()
    
    def open_customer(self, customer_id):
        if hasattr(self, 'dropdown_menu'):
            self.dropdown_menu.dismiss()
        
        # Set customer ID and switch to customer screen
        app = MDApp.get_running_app()
        app.sm.get_screen('customer').customer_id = customer_id
        app.sm.current = 'customer'
    
    def add_new_customer(self):
        app = MDApp.get_running_app()
        self.dialog = MDDialog(
            title=app.reshape_arabic("إضافة زبون جديد"),
            type="custom",
            content_cls=NewCustomerForm(),
            buttons=[
                MDFlatButton(
                    text=app.reshape_arabic("إلغاء"),
                    on_release=lambda x: self.dialog.dismiss()
                ),
                MDFlatButton(
                    text=app.reshape_arabic("إضافة"),
                    on_release=self.save_new_customer
                ),
            ],
        )
        self.dialog.open()
    
    def save_new_customer(self, *args):
        app = MDApp.get_running_app()
        form = self.dialog.content_cls
        name = form.ids.name.text
        phone = form.ids.phone.text
        page_number = form.ids.page_number.text

        if not name:
            show_snackbar(app.reshape_arabic("الرجاء إدخال اسم الزبون"))
            return

        conn = sqlite3.connect('abu_alaa_store.db')
        cursor = conn.cursor()

        # Add new customer
        cursor.execute('''
        INSERT INTO customers (name, phone, page_number)
        VALUES (?, ?, ?)
        ''', (name, phone, page_number))

        customer_id = cursor.lastrowid
        conn.commit()
        conn.close()

        self.dialog.dismiss()
        show_snackbar(app.reshape_arabic(f"تمت إضافة الزبون {name} بنجاح"))

        # Update dashboard
        self.update_summary()
    
    def import_excel(self):
        # In a real app, use a file chooser
        # For this example, we'll assume the file is in the app directory
        app = MDApp.get_running_app()
        try:
            df = pd.read_excel('customers_data.xlsx')

            # Validate data
            if 'name' not in df.columns or 'amount' not in df.columns:
                snackbar = Snackbar()
                snackbar.text = app.reshape_arabic("الملف لا يحتوي على الأعمدة المطلوبة")
                snackbar.open()
                return
            
            # Process data
            conn = sqlite3.connect('abu_alaa_store.db')
            cursor = conn.cursor()
            
            success_count = 0
            error_count = 0
            
            for _, row in df.iterrows():
                try:
                    name = row['name']
                    amount = float(row['amount'])
                    phone = str(row.get('phone', '')) if not pd.isna(row.get('phone', '')) else ''
                    page_number = str(row.get('page_number', '')) if not pd.isna(row.get('page_number', '')) else ''
                    description = str(row.get('description', '')) if not pd.isna(row.get('description', '')) else ''
                    
                    # Check if customer exists
                    cursor.execute('SELECT id FROM customers WHERE name = ?', (name,))
                    result = cursor.fetchone()
                    
                    if result:
                        customer_id = result[0]
                    else:
                        # Add new customer
                        cursor.execute('''
                        INSERT INTO customers (name, phone, page_number)
                        VALUES (?, ?, ?)
                        ''', (name, phone, page_number))
                        customer_id = cursor.lastrowid
                    
                    # Add transaction (assuming all imported amounts are debts)
                    cursor.execute('''
                    INSERT INTO transactions (customer_id, amount, is_debt, description)
                    VALUES (?, ?, ?, ?)
                    ''', (customer_id, abs(amount), amount > 0, description))
                    
                    success_count += 1
                except Exception as e:
                    print(f"Error processing row: {row}, Error: {e}")
                    error_count += 1
            
            conn.commit()
            conn.close()
            
            # Update dashboard
            self.update_summary()
            self.update_recent_transactions()
            self.update_chart()
            
            show_snackbar(app.reshape_arabic(f"تم استيراد {success_count} سجل بنجاح، {error_count} سجل به أخطاء"))

        except Exception as e:
            show_snackbar(app.reshape_arabic(f"حدث خطأ أثناء استيراد الملف: {str(e)}"))

# Customer Screen
class CustomerScreen(Screen):
    customer_id = NumericProperty(None)
    
    def on_enter(self):
        if self.customer_id is not None:
            self.load_customer_data()
    
    def load_customer_data(self):
        conn = sqlite3.connect('abu_alaa_store.db')
        cursor = conn.cursor()
        
        # Get customer info
        cursor.execute('''
        SELECT name, phone, page_number, created_at FROM customers
        WHERE id = ?
        ''', (self.customer_id,))
        
        customer = cursor.fetchone()
        if not customer:
            conn.close()
            return
        
        name, phone, page_number, created_at = customer
        
        # Update customer info in UI
        app = MDApp.get_running_app()
        self.ids.customer_name.text = name
        self.ids.customer_phone.text = phone or app.reshape_arabic("غير متوفر")
        self.ids.customer_page.text = page_number or app.reshape_arabic("غير متوفر")
        self.ids.customer_since.text = created_at
        
        # Get transactions
        cursor.execute('''
        SELECT id, amount, is_debt, description, transaction_date FROM transactions
        WHERE customer_id = ?
        ORDER BY transaction_date DESC
        ''', (self.customer_id,))
        
        transactions = cursor.fetchall()
        
        # Calculate summary
        total_debt = sum(amount for _, amount, is_debt, _, _ in transactions if is_debt)
        total_paid = sum(amount for _, amount, is_debt, _, _ in transactions if not is_debt)
        balance = total_debt - total_paid
        
        self.ids.total_debt.text = f"{total_debt:.2f}"
        self.ids.total_paid.text = f"{total_paid:.2f}"
        self.ids.balance.text = f"{balance:.2f}"
        
        # Create data table
        self.create_transactions_table(transactions)
        
        # Create chart
        self.create_customer_chart(transactions)
        
        conn.close()
    
    def create_transactions_table(self, transactions):
        # Create data table
        app = MDApp.get_running_app()
        self.data_table = MDDataTable(
            pos_hint={"center_x": 0.5, "center_y": 0.5},
            size_hint=(0.9, 0.6),
            use_pagination=True,
            column_data=[
                ("#", dp(30)),
                (app.reshape_arabic("التاريخ"), dp(60)),
                (app.reshape_arabic("النوع"), dp(30)),
                (app.reshape_arabic("المبلغ"), dp(30)),
                (app.reshape_arabic("الوصف"), dp(100)),
            ],
            row_data=[
                (
                    str(i+1),
                    transaction_date,
                    app.reshape_arabic("دين") if is_debt else app.reshape_arabic("دفعة"),
                    f"{amount:.2f}",
                    description or ""
                ) for i, (transaction_id, amount, is_debt, description, transaction_date) in enumerate(transactions)
            ],
        )
        
        # Clear previous table
        if hasattr(self.ids.table_container, 'children'):
            self.ids.table_container.clear_widgets()
        
        # Add table to the layout
        self.ids.table_container.add_widget(self.data_table)
    
    def create_customer_chart(self, transactions):
        # Group transactions by month
        monthly_data = {}
        
        for _, amount, is_debt, _, date in transactions:
            # Extract month and year from date
            try:
                date_obj = datetime.strptime(date, '%Y-%m-%d %H:%M:%S')
                month_year = date_obj.strftime('%m-%Y')
                
                if month_year not in monthly_data:
                    monthly_data[month_year] = {'debt': 0, 'payment': 0}
                
                if is_debt:
                    monthly_data[month_year]['debt'] += amount
                else:
                    monthly_data[month_year]['payment'] += amount
            except Exception as e:
                print(f"Error processing date {date}: {e}")
        
        # Sort months chronologically
        sorted_months = sorted(monthly_data.keys(), key=lambda x: datetime.strptime(x, '%m-%Y'))
        
        # Create chart
        fig, ax = plt.subplots(figsize=(8, 3))

        months = sorted_months[-6:] if len(sorted_months) > 6 else sorted_months  # Last 6 months
        debts = [monthly_data[month]['debt'] for month in months]
        payments = [monthly_data[month]['payment'] for month in months]

        app = MDApp.get_running_app()

        ax.bar(months, debts, color='red', alpha=0.7, label=app.reshape_arabic('ديون'))
        ax.bar(months, payments, color='green', alpha=0.7, label=app.reshape_arabic('مدفوعات'))

        ax.set_title(app.reshape_arabic('ملخص الديون والمدفوعات الشهرية'))
        ax.set_xlabel(app.reshape_arabic('الشهر'))
        ax.set_ylabel(app.reshape_arabic('المبلغ'))
        ax.legend()
        
        # Save chart to file
        plt.tight_layout()
        plt.savefig(f'customer_{self.customer_id}_chart.png')
        plt.close()
        
        # Update chart in UI
        self.ids.customer_chart.source = f'customer_{self.customer_id}_chart.png'
        self.ids.customer_chart.reload()
    
    def add_transaction(self):
        app = MDApp.get_running_app()
        self.dialog = MDDialog(
            title=app.reshape_arabic("إضافة معاملة جديدة"),
            type="custom",
            content_cls=NewTransactionForm(),
            buttons=[
                MDFlatButton(
                    text=app.reshape_arabic("إلغاء"),
                    on_release=lambda x: self.dialog.dismiss()
                ),
                MDFlatButton(
                    text=app.reshape_arabic("إضافة"),
                    on_release=self.save_transaction
                ),
            ],
        )
        self.dialog.open()
    
    def save_transaction(self, *args):
        app = MDApp.get_running_app()
        form = self.dialog.content_cls
        amount_text = form.ids.amount.text
        description = form.ids.description.text
        transaction_type = form.ids.transaction_type.text

        if not amount_text or not transaction_type:
            show_snackbar(app.reshape_arabic("الرجاء إدخال المبلغ ونوع المعاملة"))
            return

        try:
            amount = float(amount_text)
            is_debt = transaction_type == app.reshape_arabic("دين")

            conn = sqlite3.connect('abu_alaa_store.db')
            cursor = conn.cursor()

            # Add transaction
            cursor.execute('''
            INSERT INTO transactions (customer_id, amount, is_debt, description)
            VALUES (?, ?, ?, ?)
            ''', (self.customer_id, amount, is_debt, description))

            conn.commit()
            conn.close()

            self.dialog.dismiss()
            show_snackbar(app.reshape_arabic("تمت إضافة المعاملة بنجاح"))

            # Reload customer data
            self.load_customer_data()

        except ValueError:
            show_snackbar(app.reshape_arabic("الرجاء إدخال مبلغ صحيح"))
        except Exception as e:
            show_snackbar(app.reshape_arabic(f"حدث خطأ: {str(e)}"))
    
    def edit_customer(self):
        # Create form with current customer data
        app = MDApp.get_running_app()
        form = EditCustomerForm()
        form.ids.name.text = self.customer_data[1]
        form.ids.phone.text = self.customer_data[2]
        form.ids.page_number.text = self.customer_data[3]

        self.dialog = MDDialog(
            title=app.reshape_arabic("تعديل بيانات الزبون"),
            type="custom",
            content_cls=form,
            buttons=[
                MDFlatButton(
                    text=app.reshape_arabic("إلغاء"),
                    on_release=lambda x: self.dialog.dismiss()
                ),
                MDFlatButton(
                    text=app.reshape_arabic("حفظ"),
                    on_release=self.save_customer_edit
                ),
            ],
        )
        self.dialog.open()
    
    def save_customer_edit(self, *args):
        app = MDApp.get_running_app()
        form = self.dialog.content_cls
        name = form.ids.name.text
        phone = form.ids.phone.text
        page_number = form.ids.page_number.text

        if not name:
            show_snackbar(app.reshape_arabic("الرجاء إدخال اسم الزبون"))
            return

        conn = sqlite3.connect('abu_alaa_store.db')
        cursor = conn.cursor()

        # Update customer
        cursor.execute('''
        UPDATE customers
        SET name = ?, phone = ?, page_number = ?
        WHERE id = ?
        ''', (name, phone, page_number, self.customer_id))

        conn.commit()
        conn.close()

        self.dialog.dismiss()
        show_snackbar(app.reshape_arabic("تم تحديث بيانات الزبون بنجاح"))

        # Reload customer data
        self.load_customer_data()
    
    def go_back(self):
        self.manager.current = 'dashboard'

# Form Classes
class NewCustomerForm(MDApp):
    pass

class NewTransactionForm(MDApp):
    pass

class EditCustomerForm(MDApp):
    pass

# Run the app
if __name__ == '__main__':
    AbuAlaaStoreApp().run()